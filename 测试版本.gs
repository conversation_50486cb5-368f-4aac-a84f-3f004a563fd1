/**
 * 测试版本 - 用于验证透视表功能
 */

// 简化的配置，只测试一个表格
const TEST_SHEET_CONFIGS = [
  {
    title: 'NEXO-数据日报表',
    url: 'https://docs.google.com/spreadsheets/d/12d8JKdJQG62sftuzLWOyAkN_GIFyi3y-nvjcf8tidE0/edit?usp=sharing'
  }
];

const TEST_CONFIG = {
  FILTER_DAYS: 7,
  OUTPUT_SHEET_NAME: '测试-近7日数据'
};

/**
 * 测试主函数
 */
function testMain() {
  console.log('🧪 开始测试透视表功能...');
  
  try {
    // 创建测试数据
    const testData = createTestData();
    
    // 创建输出表格
    const outputResult = createTestOutputSheet();
    const outputSheet = outputResult.sheet;
    
    // 保存测试数据
    saveTestDataToSheet(outputSheet, testData);
    
    // 创建透视表
    createYesterdayPivotTableInSameSheet(outputSheet, testData);
    
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 创建测试数据
 */
function createTestData() {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = Utilities.formatDate(yesterday, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  
  const today = new Date();
  const todayStr = Utilities.formatDate(today, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  
  return [
    {
      date: yesterdayStr,
      dateObj: yesterday,
      cost: 100.50,
      campaign: '测试广告系列1',
      channel: 'Facebook',
      user: '张三',
      package: 'com.test.app1',
      sheetTitle: '测试工作表1',
      sourceTable: '测试表格1'
    },
    {
      date: yesterdayStr,
      dateObj: yesterday,
      cost: 200.75,
      campaign: '测试广告系列2',
      channel: 'Google',
      user: '李四',
      package: 'com.test.app1',
      sheetTitle: '测试工作表1',
      sourceTable: '测试表格1'
    },
    {
      date: yesterdayStr,
      dateObj: yesterday,
      cost: 150.25,
      campaign: '测试广告系列3',
      channel: 'Facebook',
      user: '张三',
      package: 'com.test.app2',
      sheetTitle: '测试工作表2',
      sourceTable: '测试表格1'
    },
    {
      date: todayStr,
      dateObj: today,
      cost: 300.00,
      campaign: '测试广告系列4',
      channel: 'TikTok',
      user: '王五',
      package: 'com.test.app3',
      sheetTitle: '测试工作表3',
      sourceTable: '测试表格1'
    }
  ];
}

/**
 * 创建测试输出表格
 */
function createTestOutputSheet() {
  let spreadsheet;
  try {
    spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  } catch (error) {
    spreadsheet = SpreadsheetApp.create('测试-广告数据汇总表');
    console.log(`📊 已创建新测试表格: ${spreadsheet.getName()}`);
  }
  
  const sheetName = TEST_CONFIG.OUTPUT_SHEET_NAME;
  
  // 删除现有工作表
  const existingSheet = spreadsheet.getSheetByName(sheetName);
  if (existingSheet) {
    if (spreadsheet.getSheets().length > 1) {
      spreadsheet.deleteSheet(existingSheet);
    } else {
      const tempSheet = spreadsheet.insertSheet('temp');
      spreadsheet.deleteSheet(existingSheet);
      spreadsheet.deleteSheet(tempSheet);
    }
  }
  
  // 创建新工作表
  const sheet = spreadsheet.insertSheet(sheetName);
  
  // 设置表头
  const headers = ['日期', '来源表格', '工作表', '渠道', '包名', '消耗($)', '使用人'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 格式化表头
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  
  return {
    sheet: sheet,
    spreadsheet: spreadsheet
  };
}

/**
 * 保存测试数据到工作表
 */
function saveTestDataToSheet(sheet, data) {
  if (data.length === 0) return;
  
  // 按日期排序
  data.sort((a, b) => new Date(b.date) - new Date(a.date));
  
  // 准备数据行
  const rows = data.map(row => [
    row.date,
    row.sourceTable,
    row.sheetTitle,
    row.channel,
    row.package,
    row.cost,
    row.user
  ]);
  
  // 写入数据
  const startRow = 2;
  sheet.getRange(startRow, 1, rows.length, 7).setValues(rows);
  
  // 格式化
  sheet.autoResizeColumns(1, 7);
  const costRange = sheet.getRange(2, 6, rows.length, 1);
  costRange.setNumberFormat('$#,##0.00');
  
  const dataRange = sheet.getRange(1, 1, rows.length + 1, 7);
  dataRange.setBorder(true, true, true, true, true, true);
  
  sheet.setFrozenRows(1);
  
  console.log(`💾 已保存 ${data.length} 行测试数据`);
}

// 复制主脚本中的透视表函数
function createYesterdayPivotTableInSameSheet(sheet, allData) {
  try {
    // 获取昨日日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = Utilities.formatDate(yesterday, Session.getScriptTimeZone(), 'yyyy-MM-dd');

    // 筛选昨日数据
    const yesterdayData = allData.filter(row => row.date === yesterdayStr);

    if (yesterdayData.length === 0) {
      console.log('⚠️ 没有找到昨日数据，跳过创建数据透视表');
      return;
    }

    console.log(`📊 开始在右侧创建昨日数据透视表，数据行数: ${yesterdayData.length}`);

    // 在第J列（第10列）开始创建透视表标题
    sheet.getRange('J1').setValue(`昨日数据透视表 (${yesterdayStr})`);
    sheet.getRange('J1').setFontWeight('bold').setFontSize(12);

    // 准备昨日数据源（从第J3开始）
    const headers = ['日期', '来源表格', '工作表', '渠道', '包名', '消耗($)', '使用人'];
    const dataRows = yesterdayData.map(row => [
      row.date,
      row.sourceTable,
      row.sheetTitle,
      row.channel,
      row.package,
      row.cost,
      row.user
    ]);

    const sourceData = [headers, ...dataRows];
    const sourceRange = sheet.getRange(3, 10, sourceData.length, headers.length);
    sourceRange.setValues(sourceData);
    
    console.log(`数据源范围: J3:${String.fromCharCode(74 + headers.length - 1)}${3 + sourceData.length - 1}`);
    console.log(`数据源行数: ${sourceData.length}, 列数: ${headers.length}`);

    // 在右侧创建数据透视表（从R3开始）
    const pivotTable = sheet.getRange(3, 18).createPivotTable(sourceRange);

    // 配置透视表
    // 添加行：包名（第5列，索引从1开始）
    const packageDimension = pivotTable.addRowGroup(5); // 包名是第5列
    packageDimension.showTotals(true);
    packageDimension.sortAscending();

    // 添加列：使用人（第7列，索引从1开始）
    const userDimension = pivotTable.addColumnGroup(7); // 使用人是第7列
    userDimension.showTotals(true);
    userDimension.sortAscending();

    // 添加值：消耗($)（第6列，索引从1开始）
    const costValue = pivotTable.addPivotValue(6, SpreadsheetApp.PivotTableSummarizeFunction.SUM);
    costValue.setDisplayName('消耗($)');

    // 隐藏数据源列（J到P列）
    sheet.hideColumns(10, 7);

    // 格式化透视表
    formatPivotTableInSameSheet(sheet);

    console.log(`✅ 已在右侧创建昨日数据透视表`);

  } catch (error) {
    console.error(`❌ 创建数据透视表时发生错误: ${error.message}`);
  }
}

function formatPivotTableInSameSheet(sheet) {
  try {
    // 等待透视表创建完成
    Utilities.sleep(1000);
    
    // 自动调整透视表列宽（从R列开始）
    sheet.autoResizeColumns(18, 10);

    // 设置透视表区域的数字格式
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();

    if (lastRow > 3 && lastCol >= 18) {
      // 计算透视表的实际范围
      const pivotStartRow = 3;
      const pivotStartCol = 18;
      const pivotRows = lastRow - pivotStartRow + 1;
      const pivotCols = lastCol - pivotStartCol + 1;
      
      if (pivotRows > 0 && pivotCols > 0) {
        // 添加边框到整个透视表区域
        const pivotRange = sheet.getRange(pivotStartRow, pivotStartCol, pivotRows, pivotCols);
        pivotRange.setBorder(true, true, true, true, true, true);

        // 设置表头样式（前两行）
        if (pivotRows >= 2) {
          const headerRange = sheet.getRange(pivotStartRow, pivotStartCol, 2, pivotCols);
          headerRange.setFontWeight('bold');
          headerRange.setBackground('#e8f0fe');
        }

        // 格式化数值为货币格式（从第4行开始，跳过表头）
        if (pivotRows > 2) {
          const valueRange = sheet.getRange(pivotStartRow + 2, pivotStartCol, pivotRows - 2, pivotCols);
          valueRange.setNumberFormat('$#,##0.00');
        }
      }
    }

  } catch (error) {
    console.error(`格式化透视表时发生错误: ${error.message}`);
  }
}
